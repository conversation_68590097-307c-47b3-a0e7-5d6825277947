#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright (c) 2024 crz
# Distributed under the MIT software license, see the accompanying
# file LICENSE or http://www.opensource.org/licenses/mit-license.php.

"""
Test to verify that filters are reapplied when the offers table is updated.
This test verifies the fix for the issue where coin filters would be lost
after refreshing the offers data.
"""

import json
import time
import unittest

from urllib.request import urlopen
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

from util import get_driver


def test_offers_filter_persistence(driver):
    """Test that filters persist after table refresh"""
    node1_url = "http://localhost:12701"

    # First, create some test offers with different coins
    # Create a BTC->XMR offer
    driver.get(node1_url + "/newoffer")
    time.sleep(1)

    select = Select(driver.find_element(By.ID, "coin_from"))
    select.select_by_visible_text("Bitcoin")
    select = Select(driver.find_element(By.ID, "coin_to"))
    select.select_by_visible_text("Monero")

    driver.find_element(By.NAME, "amt_from").send_keys("1")
    driver.find_element(By.NAME, "amt_to").send_keys("2")

    driver.find_element(By.NAME, "continue").click()
    WebDriverWait(driver, 20).until(
        EC.element_to_be_clickable((By.NAME, "check_offer"))
    ).click()
    WebDriverWait(driver, 20).until(
        EC.element_to_be_clickable((By.NAME, "submit_offer"))
    ).click()
    time.sleep(1)

    # Create a PART->XMR offer
    driver.get(node1_url + "/newoffer")
    time.sleep(1)

    select = Select(driver.find_element(By.ID, "coin_from"))
    select.select_by_visible_text("Particl")
    select = Select(driver.find_element(By.ID, "coin_to"))
    select.select_by_visible_text("Monero")

    driver.find_element(By.NAME, "amt_from").send_keys("3")
    driver.find_element(By.NAME, "amt_to").send_keys("4")

    driver.find_element(By.NAME, "continue").click()
    WebDriverWait(driver, 20).until(
        EC.element_to_be_clickable((By.NAME, "check_offer"))
    ).click()
    WebDriverWait(driver, 20).until(
        EC.element_to_be_clickable((By.NAME, "submit_offer"))
    ).click()
    time.sleep(1)

    # Now go to the offers page and test filtering
    driver.get(node1_url + "/offers")
    time.sleep(2)

    # Wait for the page to load and offers to be displayed
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "offers-body"))
    )
    time.sleep(2)  # Give time for JavaScript to load data

    # Count total offers before filtering
    offers_rows = driver.find_elements(By.CSS_SELECTOR, "#offers-body tr")
    total_offers_before = len(offers_rows)
    print(f"Total offers before filtering: {total_offers_before}")

    # Apply a filter - filter by Bitcoin (coin_from)
    coin_from_button = driver.find_element(By.ID, "coin_from_button")
    coin_from_button.click()
    time.sleep(0.5)

    # Wait for dropdown to appear
    WebDriverWait(driver, 5).until(
        EC.visibility_of_element_located((By.ID, "coin_from_dropdown"))
    )

    # First uncheck "Any" if it's checked
    any_checkbox = driver.find_element(
        By.CSS_SELECTOR, "#coin_from_dropdown input[value='any']"
    )
    if any_checkbox.is_selected():
        any_checkbox.click()
        time.sleep(0.2)

    # Select Bitcoin checkbox
    btc_checkbox = driver.find_element(
        By.CSS_SELECTOR, "#coin_from_dropdown input[value='1']"
    )  # BTC is usually coin ID 1
    if not btc_checkbox.is_selected():
        btc_checkbox.click()
        time.sleep(0.5)

    # Click outside to close dropdown
    driver.find_element(By.TAG_NAME, "body").click()
    time.sleep(1)

    # Wait for filter to be applied
    time.sleep(2)

    # Count offers after filtering
    offers_rows_filtered = driver.find_elements(By.CSS_SELECTOR, "#offers-body tr")
    total_offers_after_filter = len(offers_rows_filtered)
    print(f"Total offers after filtering by Bitcoin: {total_offers_after_filter}")

    # The filtered count should be less than or equal to the total
    assert (
        total_offers_after_filter <= total_offers_before
    ), "Filter should reduce or maintain offer count"

    # Now refresh the offers and check if filter persists
    refresh_button = driver.find_element(By.ID, "refreshOffers")
    refresh_button.click()

    # Wait for refresh to complete (button should become enabled again)
    WebDriverWait(driver, 10).until(
        lambda d: not refresh_button.get_attribute("disabled")
    )
    time.sleep(2)  # Give time for data to load and filters to be applied

    # Count offers after refresh
    offers_rows_after_refresh = driver.find_elements(By.CSS_SELECTOR, "#offers-body tr")
    total_offers_after_refresh = len(offers_rows_after_refresh)
    print(f"Total offers after refresh: {total_offers_after_refresh}")

    # The key test: after refresh, the filter should still be applied
    # So the count should be the same as after filtering, not the original total
    assert (
        total_offers_after_refresh == total_offers_after_filter
    ), f"Filter should persist after refresh. Expected {total_offers_after_filter}, got {total_offers_after_refresh}"

    # Additional check: verify that the filter UI still shows Bitcoin as selected
    coin_from_button.click()
    time.sleep(0.5)

    btc_checkbox_after_refresh = driver.find_element(
        By.CSS_SELECTOR, "#coin_from_dropdown input[value='1']"
    )
    assert (
        btc_checkbox_after_refresh.is_selected()
    ), "Bitcoin filter should still be selected after refresh"

    # Click outside to close dropdown
    driver.find_element(By.TAG_NAME, "body").click()

    print("Test Passed! Filters persist after table refresh.")


def run_tests():
    driver = get_driver()
    try:
        test_offers_filter_persistence(driver)
    finally:
        driver.close()


if __name__ == "__main__":
    run_tests()
